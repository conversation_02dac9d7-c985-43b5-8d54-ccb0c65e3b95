import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import dotenv from "dotenv";
import { generateRandomString } from "./randomText";
dotenv.config();

const s3 = new S3Client({
  region: process.env.AWS_REGION || "",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});
export const uploadFile = async (file: any) => {
     if (!file || !file.originalname) {
    throw new Error("File or file originalname is missing");
  }
  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key:
      generateRandomString() +
      file.originalname.replace(/\s+/g, "_").replace(/#/g, ""),
    Body: file.buffer,
    ContentType: file.mimetype,
  };
  const command = new PutObjectCommand(params);
  await s3.send(command);
  // Construct the media URL using the S3 URL
  const url = `https://${params.Bucket}.s3.${process.env.AWS_REGION}.amazonaws.com/${params.Key}`;
  return url;
};

export const deleteFile = async (url: string) => {
  try {
    const key = url.split(
      `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/`
    )[1];
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: key,
    };
    const command = new DeleteObjectCommand(params);
    await s3.send(command);
  } catch (error) {
    throw error;
  }
};

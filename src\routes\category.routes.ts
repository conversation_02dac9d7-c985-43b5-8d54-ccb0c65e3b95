import { Router } from 'express';
import {
  createCategory,
  getAllCategories,
  getCategoryDetails,
  updateCategory,
  deleteCategory
} from '../controllers/category.controller';
import { verifyToken, verifyTokenForAdmin } from '../middlewares/auth.middlewares';
import { validateMedia } from '../utils/validateMedia';

const router = Router();

// Public routes - get categories (no authentication required)
router.get('/', getAllCategories);
router.get('/:categoryId', getCategoryDetails);

// Protected routes - require authentication
// Create a new category (authenticated users only)
router.post('/', verifyTokenForAdmin, validateMedia ,createCategory);

// Update category (authenticated users only)
router.put('/:categoryId', verifyTokenForAdmin, validateMedia,updateCategory);

// Delete category (authenticated users only)
router.delete('/:categoryId', verifyTokenForAdmin, deleteCategory);

export default router;

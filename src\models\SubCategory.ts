import mongoose, { Schema } from 'mongoose';
import { ISubCategory } from '../interfaces/subcategory.interfaces';

const SubCategorySchema: Schema = new Schema({
  category: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required'],
    index: true
  },
  subCategoryName: {
    type: String,
    required: [true, 'Subcategory name is required'],
    trim: true,
    maxlength: [50, 'Subcategory name cannot exceed 50 characters'],
    minlength: [2, 'Subcategory name must be at least 2 characters']
  }
}, {
  timestamps: true
});

// Compound index to ensure unique subcategory names within each category
SubCategorySchema.index({ category: 1, subCategoryName: 1 }, { unique: true });

export default mongoose.model<ISubCategory>('SubCategory', SubCategorySchema);

import { Router } from 'express';
import { 
  createShop, 
  updateShop, 
  getUserShops, 
  getShopDetails, 
  deleteShop 
} from '../controllers/shop.controller';
import { verifyTokenForBusinessOwner } from '../middlewares/auth.middlewares';
import { validateMedia } from '../utils/validateMedia';

const router = Router();

// All shop routes require business owner authentication
router.use(verifyTokenForBusinessOwner);

// Create a new shop
router.post('/',validateMedia, createShop);

// Get all shops of the authenticated user
router.get('/', getUserShops);

// Get specific shop details
router.get('/:shopId', getShopDetails);

// Update shop information
router.put('/:shopId', validateMedia,updateShop);

// Delete shop
router.delete('/:shopId', deleteShop);

export default router;

import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import Wishlist from '../models/Wishlist';
import Product from '../models/Product';
import { IWishlist } from '../interfaces/wishlist.interface';
import User from '../models/User';

// Add product to Wishlist
export const addToWishlist = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // find user details
    const user = await User.findById(userId);
    if(!user){
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }
    if(user.role !== 'user'){
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to add products to wishlist'
      });
    }

    // Check if the product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if the product is already in the wishlist
    const existingWishlist = await Wishlist.findOne({ userId, productId });
    if (existingWishlist) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Product already in wishlist'
      });
    }

    // Add to wishlist
    const newWishlist = new Wishlist({ userId, productId });
    await newWishlist.save();

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Product added to wishlist successfully',
      wishlistItem: newWishlist
    });

  } catch (error) {
    console.error('Error adding product to wishlist:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error adding product to wishlist',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all wishlist items for a user
export const getUserWishlist = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId);
    if(!user){
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }
    if(user.role !== 'user'){
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to view wishlist'
      });
    }

    // Find wishlist items
    const wishlistItems = await Wishlist.find({ userId }).populate('productId', 'productName productMedia price');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Wishlist fetched successfully',
      wishlistItems
    });

  } catch (error) {
    console.error('Error fetching wishlist:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error fetching wishlist',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Remove product from Wishlist
export const removeFromWishlist = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId);
    if(!user){
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }
    if(user.role !== 'user'){
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to remove products from wishlist'
      });
    }

    // Check if the product exists in the wishlist
    const wishlistItem = await Wishlist.findOne({ userId, productId });
    if (!wishlistItem) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found in wishlist'
      });
    }

    // Remove the product from wishlist
    await Wishlist.findByIdAndDelete(wishlistItem._id);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product removed from wishlist successfully'
    });

  } catch (error) {
    console.error('Error removing product from wishlist:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error removing product from wishlist',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

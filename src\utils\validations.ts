// Validation utilities for authentication

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long' };
  }
  
  if (password.length > 128) {
    return { isValid: false, message: 'Password must be less than 128 characters' };
  }
  
  // Check for at least one letter and one number
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  
  if (!hasLetter || !hasNumber) {
    return { isValid: false, message: 'Password must contain at least one letter and one number' };
  }
  
  return { isValid: true };
};

export const validateUsername = (username: string): { isValid: boolean; message?: string } => {
  if (username.length < 3) {
    return { isValid: false, message: 'Username must be at least 3 characters long' };
  }
  
  if (username.length > 30) {
    return { isValid: false, message: 'Username must be less than 30 characters' };
  }
  
  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  if (!usernameRegex.test(username)) {
    return { isValid: false, message: 'Username can only contain letters, numbers, and underscores' };
  }
  
  return { isValid: true };
};

export const validatePhoneNumber = (phoneNumber: string): { isValid: boolean; message?: string } => {
  const phoneRegex = /^\+?[\d\s-()]+$/;
  if (!phoneRegex.test(phoneNumber)) {
    return { isValid: false, message: 'Please enter a valid phone number' };
  }
  
  // Remove all non-digit characters to check length
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  if (digitsOnly.length < 10 || digitsOnly.length > 15) {
    return { isValid: false, message: 'Phone number must be between 10 and 15 digits' };
  }
  
  return { isValid: true };
};


export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export const validateSignupData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Required fields check
  const requiredFields = ['fullname', 'username', 'email', 'phoneNumber', 'password', 'role'];
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(`${field} is required`);
    }
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }


  // Validate email
  if (!validateEmail(data.email)) {
    errors.push('Please enter a valid email address');
  }

  // Validate password
  const passwordValidation = validatePassword(data.password);
  if (!passwordValidation.isValid) {
    errors.push(passwordValidation.message!);
  }

  // Validate username
  const usernameValidation = validateUsername(data.username);
  if (!usernameValidation.isValid) {
    errors.push(usernameValidation.message!);
  }

  // Validate phone number
  const phoneValidation = validatePhoneNumber(data.phoneNumber);
  if (!phoneValidation.isValid) {
    errors.push(phoneValidation.message!);
  }
  return { isValid: errors.length === 0, errors };
};

// Shop validation utilities
export const validateShopName = (shopName: string): { isValid: boolean; message?: string } => {
  if (!shopName || shopName.trim().length === 0) {
    return { isValid: false, message: 'Shop name is required' };
  }

  if (shopName.trim().length < 2) {
    return { isValid: false, message: 'Shop name must be at least 2 characters long' };
  }

  if (shopName.trim().length > 100) {
    return { isValid: false, message: 'Shop name cannot exceed 100 characters' };
  }

  return { isValid: true };
};

export const validateShopImage = (shopImage: string): { isValid: boolean; message?: string } => {
  if (!shopImage || shopImage.trim().length === 0) {
    return { isValid: false, message: 'Shop image is required' };
  }

  const imageUrlRegex = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i;
  if (!imageUrlRegex.test(shopImage)) {
    return { isValid: false, message: 'Shop image must be a valid image URL (jpg, jpeg, png, gif, webp)' };
  }

  return { isValid: true };
};

export const validateCreateShopData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validate shop name
  const shopNameValidation = validateShopName(data.shopName);
  if (!shopNameValidation.isValid) {
    errors.push(shopNameValidation.message!);
  }
  return { isValid: errors.length === 0, errors };
};

export const validateUpdateShopData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Only validate provided fields for update
  if (data.shopName !== undefined) {
    const shopNameValidation = validateShopName(data.shopName);
    if (!shopNameValidation.isValid) {
      errors.push(shopNameValidation.message!);
    }
  }

  return { isValid: errors.length === 0, errors };
};

// Category validation utilities
export const validateCategoryName = (categoryName: string): { isValid: boolean; message?: string } => {
  if (!categoryName || categoryName.trim().length === 0) {
    return { isValid: false, message: 'Category name is required' };
  }

  if (categoryName.trim().length < 2) {
    return { isValid: false, message: 'Category name must be at least 2 characters long' };
  }

  if (categoryName.trim().length > 50) {
    return { isValid: false, message: 'Category name cannot exceed 50 characters' };
  }

  return { isValid: true };
};

export const validateCategoryDescription = (categoryDescription: string): { isValid: boolean; message?: string } => {
  if (!categoryDescription || categoryDescription.trim().length === 0) {
    return { isValid: false, message: 'Category description is required' };
  }

  if (categoryDescription.trim().length < 10) {
    return { isValid: false, message: 'Category description must be at least 10 characters long' };
  }

  if (categoryDescription.trim().length > 500) {
    return { isValid: false, message: 'Category description cannot exceed 500 characters' };
  }

  return { isValid: true };
};

export const validateCategoryType = (categoryType: string): { isValid: boolean; message?: string } => {
  const validTypes = ['electronics', 'clothing', 'home', 'books', 'sports', 'beauty', 'automotive', 'toys', 'food', 'other'];

  if (!categoryType || categoryType.trim().length === 0) {
    return { isValid: false, message: 'Category type is required' };
  }

  if (!validTypes.includes(categoryType.toLowerCase())) {
    return { isValid: false, message: `Category type must be one of: ${validTypes.join(', ')}` };
  }

  return { isValid: true };
};

export const validateCreateCategoryData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validate category name
  const categoryNameValidation = validateCategoryName(data.categoryName);
  if (!categoryNameValidation.isValid) {
    errors.push(categoryNameValidation.message!);
  }

  // Validate category description
  const categoryDescriptionValidation = validateCategoryDescription(data.categoryDescription);
  if (!categoryDescriptionValidation.isValid) {
    errors.push(categoryDescriptionValidation.message!);
  }

  // Validate category type
  const categoryTypeValidation = validateCategoryType(data.categoryType);
  if (!categoryTypeValidation.isValid) {
    errors.push(categoryTypeValidation.message!);
  }

  return { isValid: errors.length === 0, errors };
};

export const validateUpdateCategoryData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Only validate provided fields for update
  if (data.categoryName !== undefined) {
    const categoryNameValidation = validateCategoryName(data.categoryName);
    if (!categoryNameValidation.isValid) {
      errors.push(categoryNameValidation.message!);
    }
  }

  if (data.categoryDescription !== undefined) {
    const categoryDescriptionValidation = validateCategoryDescription(data.categoryDescription);
    if (!categoryDescriptionValidation.isValid) {
      errors.push(categoryDescriptionValidation.message!);
    }
  }

  if (data.categoryImage !== undefined) {
    const categoryImageValidation = validateShopImage(data.categoryImage); // Reuse shop image validation
    if (!categoryImageValidation.isValid) {
      errors.push(categoryImageValidation.message!.replace('Shop', 'Category'));
    }
  }

  if (data.categoryType !== undefined) {
    const categoryTypeValidation = validateCategoryType(data.categoryType);
    if (!categoryTypeValidation.isValid) {
      errors.push(categoryTypeValidation.message!);
    }
  }

  return { isValid: errors.length === 0, errors };
};

// SubCategory validation utilities
export const validateSubCategoryName = (subCategoryName: string): { isValid: boolean; message?: string } => {
  if (!subCategoryName || subCategoryName.trim().length === 0) {
    return { isValid: false, message: 'Subcategory name is required' };
  }

  if (subCategoryName.trim().length < 2) {
    return { isValid: false, message: 'Subcategory name must be at least 2 characters long' };
  }

  if (subCategoryName.trim().length > 50) {
    return { isValid: false, message: 'Subcategory name cannot exceed 50 characters' };
  }

  return { isValid: true };
};

export const validateObjectId = (id: string, fieldName: string): { isValid: boolean; message?: string } => {
  if (!id || id.trim().length === 0) {
    return { isValid: false, message: `${fieldName} is required` };
  }

  const mongoose = require('mongoose');
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return { isValid: false, message: `Invalid ${fieldName}` };
  }

  return { isValid: true };
};

export const validateCreateSubCategoryData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validate category ID
  const categoryValidation = validateObjectId(data.category, 'category ID');
  if (!categoryValidation.isValid) {
    errors.push(categoryValidation.message!);
  }

  // Validate subcategory name
  const subCategoryNameValidation = validateSubCategoryName(data.subCategoryName);
  if (!subCategoryNameValidation.isValid) {
    errors.push(subCategoryNameValidation.message!);
  }

  return { isValid: errors.length === 0, errors };
};

export const validateUpdateSubCategoryData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Only validate provided fields for update
  if (data.category !== undefined) {
    const categoryValidation = validateObjectId(data.category, 'category ID');
    if (!categoryValidation.isValid) {
      errors.push(categoryValidation.message!);
    }
  }

  if (data.subCategoryName !== undefined) {
    const subCategoryNameValidation = validateSubCategoryName(data.subCategoryName);
    if (!subCategoryNameValidation.isValid) {
      errors.push(subCategoryNameValidation.message!);
    }
  }

  return { isValid: errors.length === 0, errors };
};

// Product validation utilities
export const validateProductName = (productName: string): { isValid: boolean; message?: string } => {
  if (!productName || productName.trim().length === 0) {
    return { isValid: false, message: 'Product name is required' };
  }

  if (productName.trim().length < 2) {
    return { isValid: false, message: 'Product name must be at least 2 characters long' };
  }

  if (productName.trim().length > 100) {
    return { isValid: false, message: 'Product name cannot exceed 100 characters' };
  }

  return { isValid: true };
};

export const validateProductDescription = (productDescription: string): { isValid: boolean; message?: string } => {
  if (!productDescription || productDescription.trim().length === 0) {
    return { isValid: false, message: 'Product description is required' };
  }

  if (productDescription.trim().length < 10) {
    return { isValid: false, message: 'Product description must be at least 10 characters long' };
  }

  if (productDescription.trim().length > 1000) {
    return { isValid: false, message: 'Product description cannot exceed 1000 characters' };
  }

  return { isValid: true };
};

export const validatePrice = (price: number): { isValid: boolean; message?: string } => {
    price = Number(price);
  if (price === undefined || price === null) {
    return { isValid: false, message: 'Price is required' };
  }

  if (!Number.isFinite(price) || price < 0) {
    return { isValid: false, message: 'Price must be a valid positive number' };
  }

  return { isValid: true };
};

export const validateStockQuantity = (stockQuantity: number): { isValid: boolean; message?: string } => {
    stockQuantity = Number(stockQuantity);

  if (stockQuantity === undefined || stockQuantity === null) {
    return { isValid: false, message: 'Stock quantity is required' };
  }

  if (!Number.isInteger(stockQuantity) || stockQuantity < 0) {
    return { isValid: false, message: 'Stock quantity must be a non-negative integer' };
  }

  return { isValid: true };
};

export const validateProductMedia = (productMedia: string[]): { isValid: boolean; message?: string } => {
  if (!productMedia || !Array.isArray(productMedia) || productMedia.length === 0) {
    return { isValid: false, message: 'At least one product image is required' };
  }

  if (productMedia.length > 10) {
    return { isValid: false, message: 'Maximum 10 product images allowed' };
  }

  const imageUrlRegex = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i;
  for (const url of productMedia) {
    if (!imageUrlRegex.test(url)) {
      return { isValid: false, message: 'All product media must be valid image URLs' };
    }
  }

  return { isValid: true };
};

export const validateProductSizes = (sizes: string[]): { isValid: boolean; message?: string } => {
  const validSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];

  if (!Array.isArray(sizes)) {
    return { isValid: false, message: 'Sizes must be an array' };
  }

  for (const size of sizes) {
    if (!validSizes.includes(size)) {
      return { isValid: false, message: `Invalid size: ${size}. Valid sizes are: ${validSizes.join(', ')}` };
    }
  }

  return { isValid: true };
};

export const validateProductColors = (colors: string[]): { isValid: boolean; message?: string } => {
  if (!Array.isArray(colors)) {
    return { isValid: false, message: 'Colors must be an array' };
  }

  if (colors.length > 20) {
    return { isValid: false, message: 'Maximum 20 colors allowed' };
  }

  return { isValid: true };
};

export const validateProductTags = (tags: string[]): { isValid: boolean; message?: string } => {
  if (!Array.isArray(tags)) {
    return { isValid: false, message: 'Tags must be an array' };
  }

  if (tags.length > 10) {
    return { isValid: false, message: 'Maximum 10 tags allowed' };
  }

  return { isValid: true };
};

export const validateCreateProductData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validate product name
  const productNameValidation = validateProductName(data.productName);
  if (!productNameValidation.isValid) {
    errors.push(productNameValidation.message!);
  }

  // Validate product description
  const productDescriptionValidation = validateProductDescription(data.productDescription);
  if (!productDescriptionValidation.isValid) {
    errors.push(productDescriptionValidation.message!);
  }

  // Validate product category
  const categoryValidation = validateObjectId(data.productCategory, 'product category');
  if (!categoryValidation.isValid) {
    errors.push(categoryValidation.message!);
  }

  // Validate product subcategory
  const subCategoryValidation = validateObjectId(data.productSubCategory, 'product subcategory');
  if (!subCategoryValidation.isValid) {
    errors.push(subCategoryValidation.message!);
  }

  // Validate shop
  const shopValidation = validateObjectId(data.shop, 'shop');
  if (!shopValidation.isValid) {
    errors.push(shopValidation.message!);
  }

  // Validate price
  const priceValidation = validatePrice(data.price);
  if (!priceValidation.isValid) {
    errors.push(priceValidation.message!);
  }

  // Validate stock quantity
  const stockValidation = validateStockQuantity(data.stockQuantity);
  if (!stockValidation.isValid) {
    errors.push(stockValidation.message!);
  }

  // Validate sizes
  const sizesValidation = validateProductSizes(data.sizes || []);
  if (!sizesValidation.isValid) {
    errors.push(sizesValidation.message!);
  }

  // Validate colors
  const colorsValidation = validateProductColors(data.colors || []);
  if (!colorsValidation.isValid) {
    errors.push(colorsValidation.message!);
  }

  // Validate tags
  const tagsValidation = validateProductTags(data.tags || []);
  if (!tagsValidation.isValid) {
    errors.push(tagsValidation.message!);
  }

  return { isValid: errors.length === 0, errors };
};

export const validateUpdateProductData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Only validate provided fields for update
  if (data.productName !== undefined) {
    const productNameValidation = validateProductName(data.productName);
    if (!productNameValidation.isValid) {
      errors.push(productNameValidation.message!);
    }
  }

  if (data.productDescription !== undefined) {
    const productDescriptionValidation = validateProductDescription(data.productDescription);
    if (!productDescriptionValidation.isValid) {
      errors.push(productDescriptionValidation.message!);
    }
  }

  if (data.productCategory !== undefined) {
    const categoryValidation = validateObjectId(data.productCategory, 'product category');
    if (!categoryValidation.isValid) {
      errors.push(categoryValidation.message!);
    }
  }

  if (data.productSubCategory !== undefined) {
    const subCategoryValidation = validateObjectId(data.productSubCategory, 'product subcategory');
    if (!subCategoryValidation.isValid) {
      errors.push(subCategoryValidation.message!);
    }
  }

  if (data.price !== undefined) {
    const priceValidation = validatePrice(data.price);
    if (!priceValidation.isValid) {
      errors.push(priceValidation.message!);
    }
  }

  if (data.stockQuantity !== undefined) {
    const stockValidation = validateStockQuantity(data.stockQuantity);
    if (!stockValidation.isValid) {
      errors.push(stockValidation.message!);
    }
  }

  if (data.productMedia !== undefined) {
    const mediaValidation = validateProductMedia(data.productMedia);
    if (!mediaValidation.isValid) {
      errors.push(mediaValidation.message!);
    }
  }

  if (data.sizes !== undefined) {
    const sizesValidation = validateProductSizes(data.sizes);
    if (!sizesValidation.isValid) {
      errors.push(sizesValidation.message!);
    }
  }

  if (data.colors !== undefined) {
    const colorsValidation = validateProductColors(data.colors);
    if (!colorsValidation.isValid) {
      errors.push(colorsValidation.message!);
    }
  }

  if (data.tags !== undefined) {
    const tagsValidation = validateProductTags(data.tags);
    if (!tagsValidation.isValid) {
      errors.push(tagsValidation.message!);
    }
  }

  return { isValid: errors.length === 0, errors };
};

export const validateMarkAsSoldData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (data.quantity === undefined || data.quantity === null) {
    errors.push('Quantity is required');
  } else if (!Number.isInteger(data.quantity) || data.quantity <= 0) {
    errors.push('Quantity must be a positive integer');
  }

  return { isValid: errors.length === 0, errors };
};


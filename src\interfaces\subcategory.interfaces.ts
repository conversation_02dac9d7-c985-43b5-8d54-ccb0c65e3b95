import { Document, Types } from 'mongoose';

export interface ISubCategory extends Document {
  _id: Types.ObjectId;
  category: Types.ObjectId;
  subCategoryName: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateSubCategoryRequest {
  category: string;
  subCategoryName: string;
}

export interface IUpdateSubCategoryRequest {
  category?: string;
  subCategoryName?: string;
}

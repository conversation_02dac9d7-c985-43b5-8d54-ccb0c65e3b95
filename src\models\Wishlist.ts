import mongoose, { Schema } from 'mongoose';
import { IWishlist } from '../interfaces/wishlist.interface';


const WishlistSchema: Schema = new Schema<IWishlist>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
    index: true
  }
}, { timestamps: true });

// Create the Wishlist model
const Wishlist = mongoose.model<IWishlist>('Wishlist', WishlistSchema);

export default Wishlist;

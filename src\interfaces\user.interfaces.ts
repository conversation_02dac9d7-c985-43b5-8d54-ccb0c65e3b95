import { Document, Types } from 'mongoose';
import { TypesOfUsersEnum } from "../types/userTypes";

export interface IDeliveryAddress {
  state: string;
  city: string;
  postalCode: number;
  streetAddress: string;
}
export interface IUser extends Document {
  _id: Types.ObjectId;
  email: string;
  fullname: string;
  phoneNumber: string;
  password: string;
  role: TypesOfUsersEnum;
  isEmailVerified: boolean;
  isBusinessOwnerVerified: boolean;
  otp: string;
  otpCreatedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  deliveryAddress: IDeliveryAddress;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

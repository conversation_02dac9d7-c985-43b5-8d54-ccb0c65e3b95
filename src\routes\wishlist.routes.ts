import { Router } from 'express';
import { verifyTokenForUser } from '../middlewares/auth.middlewares';
import {
  addToWishlist,
  getUserWishlist,
  removeFromWishlist
} from '../controllers/wishlist.controller';

const router = Router();

// Protected routes - require user authentication
router.use(verifyTokenForUser);

// Add a product to the wishlist
router.post('/:productId', addToWishlist);

// Get all wishlist items for a user
router.get('/', getUserWishlist);

// Remove a product from the wishlist
router.delete('/:productId', removeFromWishlist);

export default router;

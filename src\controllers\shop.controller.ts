import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import Shop from '../models/Shop';
import { ICreateShopRequest, IUpdateShopRequest } from '../interfaces/shop.interfaces';
import { validateCreateShopData, validateUpdateShopData } from '../utils/validations';
import { uploadFile } from '../utils/mediaHandling';

// Create a new shop
export const createShop = async (req: Request, res: Response) => {
  try {
    const { shopName}: ICreateShopRequest = req.body;
    const userId = req.user?._id;
    const files = req.files as Express.Multer.File[];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    // Validate input data
    const validation = validateCreateShopData({ shopName});
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Check if shop name already exists for this user
    const existingShop = await Shop.findOne({ 
      userId: new Types.ObjectId(userId), 
      shopName: shopName.trim() 
    });

    if (existingShop) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Shop with this name already exists'
      });
    }
    if (!files || files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'At least one product image is required'
      });
    }
    console.log("files in create shop", files);
    //const shopImage = await uploadFile(files[0]);
    let url = "";
    if(files && files.length > 0)
    {
      url = await uploadFile(files[0]);
    }
    else
      {
      url = 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s';
    }
      
    console.log("url in create shop", url);

    // Create new shop
    const newShop = new Shop({
      userId: new Types.ObjectId(userId),
      shopName: shopName,
      shopImage: url,
      noOfProducts: 0
    });

    await newShop.save();

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Shop created successfully',
      shop: {
        id: newShop._id,
        shopName: newShop.shopName,
        shopImage: newShop.shopImage,
        noOfProducts: newShop.noOfProducts,
        createdAt: newShop.createdAt
      }
    });

  } catch (error) {
    console.error('Error creating shop:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating shop',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update shop information
export const updateShop = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const updateData: IUpdateShopRequest = req.body;
    const userId = req.user?._id;
    const files = req.files as Express.Multer.File[];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    // Validate input data
    const validation = validateUpdateShopData(updateData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find shop and verify ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Shop not found or you are not authorized to update this shop'
      });
    }

    // Check if new shop name already exists for this user (if shopName is being updated)
    if (updateData.shopName && updateData.shopName.trim() !== shop.shopName) {
      const existingShop = await Shop.findOne({ 
        userId: new Types.ObjectId(userId), 
        shopName: updateData.shopName.trim(),
        _id: { $ne: new Types.ObjectId(shopId) }
      });

      if (existingShop) {
        return res.status(StatusCodes.CONFLICT).json({
          success: false,
          message: 'Shop with this name already exists'
        });
      }
    }

    // Prepare update object
    const updateObject: any = {};
    if (updateData.shopName) updateObject.shopName = updateData.shopName.trim(); 

    if(files && files.length > 0) {
      const url = await uploadFile(files[0]);
      updateObject.shopImage = url;
    }
    else
      updateObject.shopImage = shop.shopImage;

    // Update shop
    const updatedShop = await Shop.findByIdAndUpdate(
      new Types.ObjectId(shopId),
      updateObject,
      { new: true, runValidators: true }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop updated successfully',
      shop: {
        id: updatedShop!._id,
        shopName: updatedShop!.shopName,
        shopImage: updatedShop!.shopImage,
        noOfProducts: updatedShop!.noOfProducts,
        updatedAt: updatedShop!.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating shop:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating shop',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all shops of a user
export const getUserShops = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    const shops = await Shop.find({ userId: new Types.ObjectId(userId) })
      .sort({ createdAt: -1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shops retrieved successfully',
      count: shops.length,
      shops: shops.map(shop => ({
        id: shop._id,
        shopName: shop.shopName,
        shopImage: shop.shopImage,
        noOfProducts: shop.noOfProducts,
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting user shops:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving shops',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get specific shop details
export const getShopDetails = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    }).select('-__v');

    if (!shop) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Shop not found or you are not authorized to view this shop'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop details retrieved successfully',
      shop: {
        id: shop._id,
        shopName: shop.shopName,
        shopImage: shop.shopImage,
        noOfProducts: shop.noOfProducts,
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting shop details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving shop details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete shop
export const deleteShop = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    // Find and delete shop (only if user owns it)
    const deletedShop = await Shop.findOneAndDelete({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    });

    if (!deletedShop) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Shop not found or you are not authorized to delete this shop'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop deleted successfully',
      deletedShop: {
        id: deletedShop._id,
        shopName: deletedShop.shopName
      }
    });

  } catch (error) {
    console.error('Error deleting shop:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting shop',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

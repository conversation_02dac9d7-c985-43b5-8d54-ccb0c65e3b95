// Simple test script to verify API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';

// Test data
const testUser = {
  email: '<EMAIL>',
  fullname: 'Test User',
  phoneNumber: '+1234567890',
  password: 'password123',
  confirmPassword: 'password123',
  role: 'businessOwner'
};

const testCategory = {
  categoryImage: 'https://example.com/category.jpg',
  categoryName: 'Test Electronics',
  categoryDescription: 'Test category for electronics items',
  categoryType: 'electronics'
};

const testSubCategory = {
  subCategoryName: 'Test Smartphones'
};

const testShop = {
  shopName: 'Test Electronics Shop',
  shopImage: 'https://example.com/shop.jpg'
};

const testProduct = {
  productName: 'Test Smartphone',
  productDescription: 'A test smartphone with great features',
  tags: ['smartphone', 'electronics', 'mobile'],
  productMedia: ['https://example.com/phone1.jpg', 'https://example.com/phone2.jpg'],
  price: 599.99,
  sizes: ['M', 'L'],
  colors: ['black', 'white', 'blue'],
  stockQuantity: 100
};

let authToken = '';
let categoryId = '';
let subCategoryId = '';
let shopId = '';
let productId = '';

async function testAPI() {
  try {
    console.log('🚀 Starting API Tests...\n');

    // Test 1: Get Categories (should be empty initially)
    console.log('1. Testing Get All Categories...');
    const categoriesResponse = await axios.get(`${BASE_URL}/categories`);
    console.log('✅ Categories retrieved:', categoriesResponse.data.count, 'categories');

    // Test 2: Get SubCategories (should be empty initially)
    console.log('\n2. Testing Get All SubCategories...');
    const subCategoriesResponse = await axios.get(`${BASE_URL}/subcategories`);
    console.log('✅ SubCategories retrieved:', subCategoriesResponse.data.count, 'subcategories');

    // Test 3: Get Products (should be empty initially)
    console.log('\n3. Testing Get All Products...');
    const productsResponse = await axios.get(`${BASE_URL}/products`);
    console.log('✅ Products retrieved:', productsResponse.data.products.length, 'products');

    // Test 4: User Signup
    console.log('\n4. Testing User Signup...');
    const signupResponse = await axios.post(`${BASE_URL}/auth/signup`, testUser);
    console.log('✅ User signup initiated:', signupResponse.data.message);

    console.log('\n🎉 Basic API tests completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Complete user verification process');
    console.log('   2. Login to get authentication token');
    console.log('   3. Test authenticated endpoints (shops, products)');
    console.log('\n📋 API Endpoints Available:');
    console.log('   Auth: POST /api/auth/signup, POST /api/auth/login');
    console.log('   Categories: GET /api/categories, POST /api/categories (auth)');
    console.log('   SubCategories: GET /api/subcategories, POST /api/subcategories (auth)');
    console.log('   Shops: GET/POST/PUT/DELETE /api/shops (business owner auth)');
    console.log('   Products: GET /api/products, CRUD /api/products (business owner auth)');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests
testAPI();

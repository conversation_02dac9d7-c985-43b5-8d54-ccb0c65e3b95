import { Router } from 'express';
import {
  createSubCategory,
  getAllSubCategories,
  getSubCategoryDetails,
  updateSubCategory,
  deleteSubCategory
} from '../controllers/subcategory.controller';
import { verifyToken } from '../middlewares/auth.middlewares';

const router = Router();

// Public routes - get subcategories (no authentication required)
router.get('/', getAllSubCategories);
router.get('/:subCategoryId', getSubCategoryDetails);

// Protected routes - require authentication
// Create a new subcategory (authenticated users only)
router.post('/', verifyToken, createSubCategory);

// Update subcategory (authenticated users only)
router.put('/:subCategoryId', verifyToken, updateSubCategory);

// Delete subcategory (authenticated users only)
router.delete('/:subCategoryId', verifyToken, deleteSubCategory);

export default router;

# OYRQ Backend API Documentation

## Overview
This backend provides a comprehensive e-commerce API with shops, categories, subcategories, and products management.

## Modules Created

### 1. Shop Module
**Files:**
- `src/models/Shop.ts` - Shop schema with user reference
- `src/interfaces/shop.interfaces.ts` - TypeScript interfaces
- `src/controllers/shop.controller.ts` - CRUD operations
- `src/routes/shop.routes.ts` - API routes

**Schema:**
- `userId` (ObjectId ref to User) - Shop owner
- `shopName` (String) - Shop name (2-100 chars)
- `shopImage` (String) - Shop image URL
- `noOfProducts` (Number) - Auto-updated product count

**Endpoints:** (All require Business Owner authentication)
- `GET /api/shops` - Get all user shops
- `POST /api/shops` - Create new shop
- `GET /api/shops/:shopId` - Get shop details
- `PUT /api/shops/:shopId` - Update shop
- `DELETE /api/shops/:shopId` - Delete shop

### 2. Category Module
**Files:**
- `src/models/Category.ts` - Category schema
- `src/interfaces/category.interfaces.ts` - TypeScript interfaces
- `src/controllers/category.controller.ts` - CRUD operations
- `src/routes/category.routes.ts` - API routes

**Schema:**
- `categoryImage` (String) - Category image URL
- `categoryName` (String) - Unique category name (2-50 chars)
- `categoryDescription` (String) - Description (10-500 chars)
- `categoryType` (String) - Enum: electronics, clothing, home, books, sports, beauty, automotive, toys, food, other

**Endpoints:**
- `GET /api/categories` - Get all categories (Public)
- `GET /api/categories/:categoryId` - Get category details (Public)
- `POST /api/categories` - Create category (Authenticated)
- `PUT /api/categories/:categoryId` - Update category (Authenticated)
- `DELETE /api/categories/:categoryId` - Delete category (Authenticated)

### 3. SubCategory Module
**Files:**
- `src/models/SubCategory.ts` - SubCategory schema
- `src/interfaces/subcategory.interfaces.ts` - TypeScript interfaces
- `src/controllers/subcategory.controller.ts` - CRUD operations
- `src/routes/subcategory.routes.ts` - API routes

**Schema:**
- `category` (ObjectId ref to Category) - Parent category
- `subCategoryName` (String) - Subcategory name (2-50 chars)
- Unique constraint: category + subCategoryName

**Endpoints:**
- `GET /api/subcategories` - Get all subcategories (Public)
- `GET /api/subcategories?categoryId=:id` - Filter by category (Public)
- `GET /api/subcategories/:subCategoryId` - Get subcategory details (Public)
- `POST /api/subcategories` - Create subcategory (Authenticated)
- `PUT /api/subcategories/:subCategoryId` - Update subcategory (Authenticated)
- `DELETE /api/subcategories/:subCategoryId` - Delete subcategory (Authenticated)

### 4. Product Module
**Files:**
- `src/models/Product.ts` - Product schema with references
- `src/interfaces/product.interfaces.ts` - TypeScript interfaces
- `src/controllers/product.controller.ts` - Advanced CRUD operations
- `src/routes/product.routes.ts` - API routes

**Schema:**
- `productName` (String) - Product name (2-100 chars)
- `productDescription` (String) - Description (10-1000 chars)
- `productCategory` (ObjectId ref to Category)
- `productSubCategory` (ObjectId ref to SubCategory)
- `shop` (ObjectId ref to Shop)
- `tags` (String[]) - Max 10 tags
- `isActive` (Boolean) - Default true
- `productMedia` (String[]) - 1-10 image URLs
- `price` (Number) - Non-negative price
- `sizes` (String[]) - XS, S, M, L, XL, XXL, XXXL
- `colors` (String[]) - Max 20 colors
- `stockQuantity` (Number) - Available stock
- `stockSold` (Number) - Sold quantity
- `isLowStock` (Boolean) - Auto-set when stock < 50

**Endpoints:**
- `GET /api/products` - Get all products with filtering (Public)
- `GET /api/products/:productId` - Get product details (Public)
- `GET /api/products/:productId/stock` - Get stock info (Public)
- `POST /api/products` - Create product (Business Owner)
- `PUT /api/products/:productId` - Update product (Business Owner)
- `DELETE /api/products/:productId` - Delete product (Business Owner)
- `PATCH /api/products/:productId/mark-sold` - Mark as sold (Business Owner)
- `GET /api/products/shop/:shopId` - Get shop products (Business Owner)
- `GET /api/products/shop/:shopId/details` - Get shop details with product count (Business Owner)

## Authentication
- **Public**: No authentication required
- **Authenticated**: Requires valid JWT token
- **Business Owner**: Requires business owner role and verified email

## Validation Features
- Comprehensive input validation for all fields
- Image URL validation
- Stock quantity management
- Automatic low stock detection
- User authorization checks
- Reference validation (category, subcategory, shop ownership)

## Business Logic
- Shop product count auto-updates when products are added/deleted
- Stock management with sold tracking
- Low stock alerts when quantity < 50
- User can only manage their own shops and products
- Subcategories must belong to valid categories
- Products must reference valid categories, subcategories, and user-owned shops

## Error Handling
- Proper HTTP status codes
- Detailed error messages
- Input validation errors
- Authorization errors
- Resource not found errors

## Testing
Run the test scripts:
- `node test-api.js` - Basic public endpoint tests
- `node test-authenticated-api.js` - Comprehensive tests

## Next Steps for Full Testing
1. Complete user email verification
2. Login to get authentication token
3. Test all authenticated endpoints
4. Verify business logic (stock management, product counting, etc.)

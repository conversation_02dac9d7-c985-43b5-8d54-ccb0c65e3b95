import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import SubCategory from '../models/SubCategory';
import Category from '../models/Category';
import { ICreateSubCategoryRequest, IUpdateSubCategoryRequest } from '../interfaces/subcategory.interfaces';
import { validateCreateSubCategoryData, validateUpdateSubCategoryData } from '../utils/validations';

// Create a new subcategory
export const createSubCategory = async (req: Request, res: Response) => {
  try {
    const { category, subCategoryName }: ICreateSubCategoryRequest = req.body;

    // Validate input data
    const validation = validateCreateSubCategoryData({ category, subCategoryName });
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Check if category exists
    const categoryExists = await Category.findById(new Types.ObjectId(category));
    if (!categoryExists) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Check if subcategory name already exists in this category
    const existingSubCategory = await SubCategory.findOne({ 
      category: new Types.ObjectId(category),
      subCategoryName: subCategoryName.trim() 
    });

    if (existingSubCategory) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Subcategory with this name already exists in this category'
      });
    }

    // Create new subcategory
    const newSubCategory = new SubCategory({
      category: new Types.ObjectId(category),
      subCategoryName: subCategoryName.trim()
    });

    await newSubCategory.save();

    // Populate category details for response
    await newSubCategory.populate('category', 'categoryName categoryType');

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Subcategory created successfully',
      subCategory: {
        id: newSubCategory._id,
        category: newSubCategory.category,
        subCategoryName: newSubCategory.subCategoryName,
        createdAt: newSubCategory.createdAt
      }
    });

  } catch (error) {
    console.error('Error creating subcategory:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating subcategory',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all subcategories
export const getAllSubCategories = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.query;
    
    // Build filter object
    const filter: any = {};
    if (categoryId && typeof categoryId === 'string') {
      if (!Types.ObjectId.isValid(categoryId)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Invalid category ID'
        });
      }
      filter.category = new Types.ObjectId(categoryId);
    }

    const subCategories = await SubCategory.find(filter)
      .populate('category', 'categoryName categoryType categoryImage')
      .sort({ subCategoryName: 1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Subcategories retrieved successfully',
      count: subCategories.length,
      subCategories: subCategories.map(subCategory => ({
        id: subCategory._id,
        category: subCategory.category,
        subCategoryName: subCategory.subCategoryName,
        createdAt: subCategory.createdAt,
        updatedAt: subCategory.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting subcategories:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving subcategories',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get specific subcategory details
export const getSubCategoryDetails = async (req: Request, res: Response) => {
  try {
    const { subCategoryId } = req.params;

    if (!Types.ObjectId.isValid(subCategoryId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid subcategory ID'
      });
    }

    const subCategory = await SubCategory.findById(new Types.ObjectId(subCategoryId))
      .populate('category', 'categoryName categoryType categoryImage categoryDescription')
      .select('-__v');

    if (!subCategory) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Subcategory not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Subcategory details retrieved successfully',
      subCategory: {
        id: subCategory._id,
        category: subCategory.category,
        subCategoryName: subCategory.subCategoryName,
        createdAt: subCategory.createdAt,
        updatedAt: subCategory.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting subcategory details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving subcategory details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update subcategory
export const updateSubCategory = async (req: Request, res: Response) => {
  try {
    const { subCategoryId } = req.params;
    const updateData: IUpdateSubCategoryRequest = req.body;

    if (!Types.ObjectId.isValid(subCategoryId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid subcategory ID'
      });
    }

    // Validate input data
    const validation = validateUpdateSubCategoryData(updateData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find subcategory
    const subCategory = await SubCategory.findById(new Types.ObjectId(subCategoryId));

    if (!subCategory) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Subcategory not found'
      });
    }

    // If category is being updated, check if it exists
    if (updateData.category) {
      const categoryExists = await Category.findById(new Types.ObjectId(updateData.category));
      if (!categoryExists) {
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: 'Category not found'
        });
      }
    }

    // Check if new subcategory name already exists in the target category
    if (updateData.subCategoryName) {
      const targetCategoryId = updateData.category ? new Types.ObjectId(updateData.category) : subCategory.category;
      const existingSubCategory = await SubCategory.findOne({ 
        category: targetCategoryId,
        subCategoryName: updateData.subCategoryName.trim(),
        _id: { $ne: new Types.ObjectId(subCategoryId) }
      });

      if (existingSubCategory) {
        return res.status(StatusCodes.CONFLICT).json({
          success: false,
          message: 'Subcategory with this name already exists in this category'
        });
      }
    }

    // Prepare update object
    const updateObject: any = {};
    if (updateData.category) updateObject.category = new Types.ObjectId(updateData.category);
    if (updateData.subCategoryName) updateObject.subCategoryName = updateData.subCategoryName.trim();

    // Update subcategory
    const updatedSubCategory = await SubCategory.findByIdAndUpdate(
      new Types.ObjectId(subCategoryId),
      updateObject,
      { new: true, runValidators: true }
    ).populate('category', 'categoryName categoryType');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Subcategory updated successfully',
      subCategory: {
        id: updatedSubCategory!._id,
        category: updatedSubCategory!.category,
        subCategoryName: updatedSubCategory!.subCategoryName,
        updatedAt: updatedSubCategory!.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating subcategory:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating subcategory',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete subcategory
export const deleteSubCategory = async (req: Request, res: Response) => {
  try {
    const { subCategoryId } = req.params;

    if (!Types.ObjectId.isValid(subCategoryId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid subcategory ID'
      });
    }

    // Find and delete subcategory
    const deletedSubCategory = await SubCategory.findByIdAndDelete(new Types.ObjectId(subCategoryId))
      .populate('category', 'categoryName');

    if (!deletedSubCategory) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Subcategory not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Subcategory deleted successfully',
      deletedSubCategory: {
        id: deletedSubCategory._id,
        subCategoryName: deletedSubCategory.subCategoryName,
        category: deletedSubCategory.category
      }
    });

  } catch (error) {
    console.error('Error deleting subcategory:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting subcategory',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

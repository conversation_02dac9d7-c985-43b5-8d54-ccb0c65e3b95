# Manual Testing Guide

## Prerequisites
1. Server running on `http://localhost:5001`
2. MongoDB connected
3. Email service configured for OTP

## Step-by-Step Testing

### 1. User Registration and Authentication

#### Signup
```bash
curl -X POST http://localhost:5001/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "fullname": "Test Business Owner",
    "phoneNumber": "+1234567890",
    "password": "password123",
    "confirmPassword": "password123",
    "role": "businessOwner"
  }'
```

#### Verify OTP (Check your email for OTP)
```bash
curl -X POST http://localhost:5001/api/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "YOUR_OTP_HERE"
  }'
```

#### Login
```bash
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "role": "businessOwner"
  }'
```

**Save the accessToken from login response for subsequent requests**

### 2. Category Testing

#### Create Category
```bash
curl -X POST http://localhost:5001/api/categories \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "categoryImage": "https://example.com/electronics.jpg",
    "categoryName": "Electronics",
    "categoryDescription": "Electronic devices and accessories",
    "categoryType": "electronics"
  }'
```

#### Get All Categories
```bash
curl -X GET http://localhost:5001/api/categories
```

### 3. SubCategory Testing

#### Create SubCategory (use categoryId from previous response)
```bash
curl -X POST http://localhost:5001/api/subcategories \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "category": "CATEGORY_ID_HERE",
    "subCategoryName": "Smartphones"
  }'
```

#### Get SubCategories by Category
```bash
curl -X GET "http://localhost:5001/api/subcategories?categoryId=CATEGORY_ID_HERE"
```

### 4. Shop Testing

#### Create Shop
```bash
curl -X POST http://localhost:5001/api/shops \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "shopName": "Tech Electronics Store",
    "shopImage": "https://example.com/shop.jpg"
  }'
```

#### Get User Shops
```bash
curl -X GET http://localhost:5001/api/shops \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 5. Product Testing

#### Create Product (use IDs from previous responses)
```bash
curl -X POST http://localhost:5001/api/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "productName": "iPhone 15 Pro",
    "productDescription": "Latest iPhone with advanced features and excellent camera quality",
    "productCategory": "CATEGORY_ID_HERE",
    "productSubCategory": "SUBCATEGORY_ID_HERE",
    "shop": "SHOP_ID_HERE",
    "tags": ["smartphone", "electronics", "apple"],
    "productMedia": ["https://example.com/iphone1.jpg", "https://example.com/iphone2.jpg"],
    "price": 999.99,
    "sizes": ["M", "L"],
    "colors": ["black", "white", "blue"],
    "stockQuantity": 100
  }'
```

#### Get Shop Products
```bash
curl -X GET http://localhost:5001/api/products/shop/SHOP_ID_HERE \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Mark Product as Sold
```bash
curl -X PATCH http://localhost:5001/api/products/PRODUCT_ID_HERE/mark-sold \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "quantity": 5
  }'
```

#### Get Product Stock
```bash
curl -X GET http://localhost:5001/api/products/PRODUCT_ID_HERE/stock
```

### 6. Advanced Testing

#### Get Shop Details with Product Count
```bash
curl -X GET http://localhost:5001/api/products/shop/SHOP_ID_HERE/details \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Filter Products
```bash
# By category
curl -X GET "http://localhost:5001/api/products?categoryId=CATEGORY_ID_HERE"

# By price range
curl -X GET "http://localhost:5001/api/products?minPrice=100&maxPrice=1000"

# Low stock products
curl -X GET "http://localhost:5001/api/products?isLowStock=true"

# Search products
curl -X GET "http://localhost:5001/api/products?search=iphone"
```

## Expected Behaviors

1. **Shop Product Count**: Automatically updates when products are added/deleted
2. **Stock Management**: 
   - `stockQuantity` decreases when marked as sold
   - `stockSold` increases when marked as sold
   - `isLowStock` becomes true when stock < 50
3. **Authorization**: Users can only manage their own shops and products
4. **Validation**: All inputs are validated with proper error messages
5. **References**: All foreign key references are validated

## Testing Checklist

- [ ] User signup and verification
- [ ] User login and token generation
- [ ] Category CRUD operations
- [ ] SubCategory CRUD operations
- [ ] Shop CRUD operations
- [ ] Product CRUD operations
- [ ] Stock management (mark as sold)
- [ ] Product filtering and search
- [ ] Authorization checks
- [ ] Validation error handling
- [ ] Shop product count updates
- [ ] Low stock detection
